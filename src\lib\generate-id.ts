/**
 * Utility functions for generating custom IDs
 */

import { db } from "@/lib/prisma";

/**
 * Generates a custom product ID with the format PRD-{idCompany}-{year}-{sequentialNumber}
 * where {idCompany} is the company ID of the user,
 * {year} is the current year (e.g., 2024), and
 * {sequentialNumber} is a sequential 6-digit number starting from 000001 for each company per year
 *
 * @param userId The ID of the user creating the product
 * @returns A custom product ID string
 */
export async function generateProductId(userId: string): Promise<string> {
  // Get the company ID for the user
  const user = await db.user.findUnique({
    where: { id: userId },
    select: { companyId: true },
  });

  if (!user?.companyId) {
    throw new Error("User does not have a company ID");
  }

  const companyId = user.companyId;

  // Get the current year
  const currentYear = new Date().getFullYear();

  // Get the count of existing products for this company in the current year
  const productCount = await db.product.count({
    where: {
      userId,
      id: {
        startsWith: `PRD-${companyId}-${currentYear}-`,
      },
    },
  });

  // Calculate the next number (add 1 to the current count)
  const nextNumber = productCount + 1;

  // Format the number as a 6-digit string with leading zeros
  const formattedNumber = String(nextNumber).padStart(6, "0");

  // Combine to create the product ID with company ID and year
  return `PRD-${companyId}-${currentYear}-${formattedNumber}`;
}

/**
 * Generates a custom purchase ID with the format PUR-{idCompany}-{year}-{sequentialNumber}
 * where {idCompany} is the company ID of the user,
 * {year} is the current year (e.g., 2024), and
 * {sequentialNumber} is a sequential 6-digit number starting from 000001 for each company per year
 *
 * @param userId The ID of the user creating the purchase
 * @returns A custom purchase ID string
 */
export async function generatePurchaseId(userId: string): Promise<string> {
  // Get the company ID for the user
  const user = await db.user.findUnique({
    where: { id: userId },
    select: { companyId: true },
  });

  if (!user?.companyId) {
    throw new Error("User does not have a company ID");
  }

  const companyId = user.companyId;

  // Get the current year
  const currentYear = new Date().getFullYear();

  // Get the count of existing purchases for this company in the current year
  const purchaseCount = await db.purchase.count({
    where: {
      userId,
      id: {
        startsWith: `PUR-${companyId}-${currentYear}-`,
      },
    },
  });

  // Calculate the next number (add 1 to the current count)
  const nextNumber = purchaseCount + 1;

  // Format the number as a 6-digit string with leading zeros
  const formattedNumber = String(nextNumber).padStart(6, "0");

  // Combine to create the purchase ID with company ID and year
  return `PUR-${companyId}-${currentYear}-${formattedNumber}`;
}

/**
 * Generates a custom sales ID with the format SAL-{idCompany}-{year}-{sequentialNumber}
 * where {idCompany} is the company ID of the user,
 * {year} is the current year (e.g., 2024), and
 * {sequentialNumber} is a sequential 6-digit number starting from 000001 for each company per year
 *
 * @param userId The ID of the user creating the sale
 * @returns A custom sales ID string
 */
export async function generateSalesId(userId: string): Promise<string> {
  // Get the company ID for the user
  const user = await db.user.findUnique({
    where: { id: userId },
    select: { companyId: true },
  });

  if (!user?.companyId) {
    throw new Error("User does not have a company ID");
  }

  const companyId = user.companyId;

  // Get the current year
  const currentYear = new Date().getFullYear();

  // Get the count of existing sales for this company in the current year
  const salesCount = await db.sale.count({
    where: {
      userId,
      id: {
        startsWith: `SAL-${companyId}-${currentYear}-`,
      },
    },
  });

  // Calculate the next number (add 1 to the current count)
  const nextNumber = salesCount + 1;

  // Format the number as a 6-digit string with leading zeros
  const formattedNumber = String(nextNumber).padStart(6, "0");

  // Combine to create the sales ID with company ID and year
  return `SAL-${companyId}-${currentYear}-${formattedNumber}`;
}
