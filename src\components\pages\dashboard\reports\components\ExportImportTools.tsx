"use client";

import React, { useState, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Progress } from "@/components/ui/progress";
import {
  Download,
  Upload,
  FileText,
  FileSpreadsheet,
  FileImage,
  Printer,
  Share2,
  Calendar,
  Settings,
} from "lucide-react";
import * as XLSX from "xlsx";
import { toast } from "sonner";
import {
  getSalesReportData,
  getPurchaseReportData,
  getProductReportData,
} from "@/actions/reports/reports";

interface FilterState {
  dateRange: string;
  startDate?: Date;
  endDate?: Date;
  category?: string;
  supplier?: string;
  customer?: string;
  status?: string;
}

interface ExportImportToolsProps {
  filters: FilterState;
}

interface ExportConfig {
  reportType: "harian" | "bulanan" | "tahunan";
  sections: {
    penjualan: boolean;
    pembelian: boolean;
    produk: boolean;
  };
  format: "excel" | "csv" | "pdf";
  includeCharts: boolean;
  includeSummary: boolean;
}

export const ExportImportTools: React.FC<ExportImportToolsProps> = ({
  filters,
}) => {
  const [isExporting, setIsExporting] = useState(false);
  const [isImporting, setIsImporting] = useState(false);
  const [exportProgress, setExportProgress] = useState(0);
  const [importProgress, setImportProgress] = useState(0);
  const [showExportDialog, setShowExportDialog] = useState(false);
  const [exportConfig, setExportConfig] = useState<ExportConfig>({
    reportType: "bulanan",
    sections: {
      penjualan: true,
      pembelian: true,
      produk: true,
    },
    format: "excel",
    includeCharts: false,
    includeSummary: true,
  });
  const fileInputRef = useRef<HTMLInputElement>(null);

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat("id-ID", {
      style: "currency",
      currency: "IDR",
      minimumFractionDigits: 0,
    }).format(value);
  };

  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString("id-ID");
    } catch (error) {
      return dateString;
    }
  };

  const exportToExcel = async (
    type: "sales" | "purchases" | "products" | "all"
  ) => {
    setIsExporting(true);
    setExportProgress(0);

    try {
      const workbook = XLSX.utils.book_new();

      if (type === "sales" || type === "all") {
        setExportProgress(25);
        const salesResult = await getSalesReportData(filters.dateRange);
        if (salesResult.success && salesResult.data) {
          const salesData = salesResult.data.map((sale: any) => ({
            ID: sale.id,
            Tanggal: formatDate(sale.date),
            Pelanggan: sale.customer || "Pelanggan Umum",
            "Jumlah Item": sale.items,
            Total: sale.total,
            "Nomor Transaksi": sale.transactionNumber || "-",
          }));
          const salesSheet = XLSX.utils.json_to_sheet(salesData);
          XLSX.utils.book_append_sheet(workbook, salesSheet, "Penjualan");
        }
      }

      if (type === "purchases" || type === "all") {
        setExportProgress(50);
        const purchaseResult = await getPurchaseReportData(filters.dateRange);
        if (purchaseResult.success && purchaseResult.data) {
          const purchaseData = purchaseResult.data.map((purchase: any) => ({
            ID: purchase.id,
            Tanggal: formatDate(purchase.date),
            Supplier: purchase.supplier || "Tidak ada supplier",
            "Jumlah Item": purchase.items,
            Total: purchase.total,
            "Referensi Invoice": purchase.invoiceRef || "-",
          }));
          const purchaseSheet = XLSX.utils.json_to_sheet(purchaseData);
          XLSX.utils.book_append_sheet(workbook, purchaseSheet, "Pembelian");
        }
      }

      if (type === "products" || type === "all") {
        setExportProgress(75);
        const productResult = await getProductReportData(filters.dateRange);
        if (productResult.success && productResult.data) {
          const productData = productResult.data.map((product: any) => ({
            ID: product.id,
            "Nama Produk": product.name,
            Kategori: product.category || "Tidak ada kategori",
            Stok: product.stock,
            Terjual: product.sold,
            Pendapatan: product.revenue,
            Keuntungan: product.profit,
          }));
          const productSheet = XLSX.utils.json_to_sheet(productData);
          XLSX.utils.book_append_sheet(workbook, productSheet, "Produk");
        }
      }

      setExportProgress(100);

      const fileName = `laporan-${type}-${new Date().toISOString().split("T")[0]}.xlsx`;
      XLSX.writeFile(workbook, fileName);

      toast.success(`Laporan ${type} berhasil diekspor!`);
    } catch (error) {
      console.error("Export error:", error);
      toast.error("Gagal mengekspor laporan");
    } finally {
      setIsExporting(false);
      setExportProgress(0);
    }
  };

  const generateAdvancedExport = async () => {
    setIsExporting(true);
    setExportProgress(0);

    try {
      const workbook = XLSX.utils.book_new();
      let progressStep = 0;
      const totalSteps = Object.values(exportConfig.sections).filter(
        Boolean
      ).length;

      // Create summary sheet if enabled
      if (exportConfig.includeSummary) {
        const summaryData = [
          ["Laporan Keuangan", ""],
          ["Periode", getReportPeriodText()],
          ["Tanggal Export", new Date().toLocaleDateString("id-ID")],
          ["", ""],
          ["Ringkasan", ""],
        ];

        const summarySheet = XLSX.utils.aoa_to_sheet(summaryData);
        XLSX.utils.book_append_sheet(workbook, summarySheet, "Ringkasan");
      }

      // Export Sales Data
      if (exportConfig.sections.penjualan) {
        setExportProgress((++progressStep / totalSteps) * 80);
        const salesResult = await getSalesReportData(
          getDateRangeForReportType()
        );
        if (salesResult.success && salesResult.data) {
          const salesData = salesResult.data.map((sale: any) => ({
            ID: sale.id,
            Tanggal: formatDate(sale.date),
            Pelanggan: sale.customer || "Pelanggan Umum",
            "Jumlah Item": sale.items,
            Total: formatCurrency(sale.total),
            "Nomor Transaksi": sale.transactionNumber || "-",
          }));

          const salesSheet = XLSX.utils.json_to_sheet(salesData);
          XLSX.utils.book_append_sheet(
            workbook,
            salesSheet,
            `Penjualan ${exportConfig.reportType.charAt(0).toUpperCase() + exportConfig.reportType.slice(1)}`
          );
        }
      }

      // Export Purchase Data
      if (exportConfig.sections.pembelian) {
        setExportProgress((++progressStep / totalSteps) * 80);
        const purchaseResult = await getPurchaseReportData(
          getDateRangeForReportType()
        );
        if (purchaseResult.success && purchaseResult.data) {
          const purchaseData = purchaseResult.data.map((purchase: any) => ({
            ID: purchase.id,
            Tanggal: formatDate(purchase.date),
            Supplier: purchase.supplier || "Tidak ada supplier",
            "Jumlah Item": purchase.items,
            Total: formatCurrency(purchase.total),
            "Referensi Invoice": purchase.invoiceRef || "-",
          }));

          const purchaseSheet = XLSX.utils.json_to_sheet(purchaseData);
          XLSX.utils.book_append_sheet(
            workbook,
            purchaseSheet,
            `Pembelian ${exportConfig.reportType.charAt(0).toUpperCase() + exportConfig.reportType.slice(1)}`
          );
        }
      }

      // Export Product Data
      if (exportConfig.sections.produk) {
        setExportProgress((++progressStep / totalSteps) * 80);
        const productResult = await getProductReportData(
          getDateRangeForReportType()
        );
        if (productResult.success && productResult.data) {
          const productData = productResult.data.map((product: any) => ({
            ID: product.id,
            "Nama Produk": product.name,
            Kategori: product.category || "Tidak ada kategori",
            Stok: product.stock,
            Terjual: product.sold,
            Pendapatan: formatCurrency(product.revenue || 0),
            Keuntungan: formatCurrency(product.profit || 0),
          }));

          const productSheet = XLSX.utils.json_to_sheet(productData);
          XLSX.utils.book_append_sheet(
            workbook,
            productSheet,
            `Produk ${exportConfig.reportType.charAt(0).toUpperCase() + exportConfig.reportType.slice(1)}`
          );
        }
      }

      setExportProgress(100);

      const fileName = `laporan-${exportConfig.reportType}-${new Date().toISOString().split("T")[0]}.xlsx`;
      XLSX.writeFile(workbook, fileName);

      toast.success(`Laporan ${exportConfig.reportType} berhasil diekspor!`);
      setShowExportDialog(false);
    } catch (error) {
      console.error("Export error:", error);
      toast.error("Gagal mengekspor laporan");
    } finally {
      setIsExporting(false);
      setExportProgress(0);
    }
  };

  const getDateRangeForReportType = () => {
    switch (exportConfig.reportType) {
      case "harian":
        return "today";
      case "bulanan":
        return "month";
      case "tahunan":
        return "year";
      default:
        return filters.dateRange;
    }
  };

  const getReportPeriodText = () => {
    const now = new Date();
    switch (exportConfig.reportType) {
      case "harian":
        return `Harian - ${now.toLocaleDateString("id-ID")}`;
      case "bulanan":
        return `Bulanan - ${now.toLocaleDateString("id-ID", { month: "long", year: "numeric" })}`;
      case "tahunan":
        return `Tahunan - ${now.getFullYear()}`;
      default:
        return "Custom";
    }
  };

  const exportToPDF = async () => {
    toast.info("Fitur export PDF akan segera hadir!");
  };

  const exportToCSV = async (type: "sales" | "purchases" | "products") => {
    try {
      let data: any[] = [];
      let headers: string[] = [];
      let filename = "";

      switch (type) {
        case "sales":
          const salesResult = await getSalesReportData(filters.dateRange);
          if (salesResult.success && salesResult.data) {
            headers = ["ID", "Tanggal", "Pelanggan", "Jumlah Item", "Total"];
            data = salesResult.data.map((sale: any) => [
              sale.id,
              formatDate(sale.date),
              sale.customer || "Pelanggan Umum",
              sale.items,
              sale.total,
            ]);
            filename = "laporan-penjualan.csv";
          }
          break;
        case "purchases":
          const purchaseResult = await getPurchaseReportData(filters.dateRange);
          if (purchaseResult.success && purchaseResult.data) {
            headers = ["ID", "Tanggal", "Supplier", "Jumlah Item", "Total"];
            data = purchaseResult.data.map((purchase: any) => [
              purchase.id,
              formatDate(purchase.date),
              purchase.supplier || "Tidak ada supplier",
              purchase.items,
              purchase.total,
            ]);
            filename = "laporan-pembelian.csv";
          }
          break;
        case "products":
          const productResult = await getProductReportData(filters.dateRange);
          if (productResult.success && productResult.data) {
            headers = [
              "ID",
              "Nama Produk",
              "Kategori",
              "Stok",
              "Terjual",
              "Pendapatan",
            ];
            data = productResult.data.map((product: any) => [
              product.id,
              product.name,
              product.category || "Tidak ada kategori",
              product.stock,
              product.sold,
              product.revenue,
            ]);
            filename = "laporan-produk.csv";
          }
          break;
      }

      if (data.length === 0) {
        toast.error("Tidak ada data untuk diekspor");
        return;
      }

      const csvContent = [headers, ...data]
        .map((row) => row.map((field) => `"${field}"`).join(","))
        .join("\n");

      const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
      const link = document.createElement("a");
      link.href = URL.createObjectURL(blob);
      link.download = filename;
      link.click();

      toast.success(`File CSV berhasil diekspor!`);
    } catch (error) {
      console.error("CSV export error:", error);
      toast.error("Gagal mengekspor file CSV");
    }
  };

  const handleImport = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setIsImporting(true);
    setImportProgress(0);

    try {
      const reader = new FileReader();
      reader.onload = async (e) => {
        try {
          setImportProgress(50);
          const data = new Uint8Array(e.target?.result as ArrayBuffer);
          const workbook = XLSX.read(data, { type: "array" });

          // Process the imported data here
          setImportProgress(100);
          toast.success("File berhasil diimpor!");
        } catch (error) {
          console.error("Import processing error:", error);
          toast.error("Gagal memproses file yang diimpor");
        } finally {
          setIsImporting(false);
          setImportProgress(0);
        }
      };
      reader.readAsArrayBuffer(file);
    } catch (error) {
      console.error("Import error:", error);
      toast.error("Gagal mengimpor file");
      setIsImporting(false);
      setImportProgress(0);
    }
  };

  const printReport = () => {
    window.print();
  };

  const shareReport = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: "Laporan Keuangan",
          text: "Laporan keuangan dari aplikasi kasir online",
          url: window.location.href,
        });
      } catch (error) {
        console.error("Error sharing:", error);
      }
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(window.location.href);
      toast.success("Link laporan disalin ke clipboard!");
    }
  };

  return (
    <div className="flex items-center gap-2">
      {/* Advanced Export Dialog */}
      <Dialog open={showExportDialog} onOpenChange={setShowExportDialog}>
        <DialogTrigger asChild>
          <Button variant="outline" className="flex items-center gap-2">
            <Download className="h-4 w-4" />
            Export Laporan
          </Button>
        </DialogTrigger>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              Konfigurasi Export Laporan
            </DialogTitle>
            <DialogDescription>
              Pilih jenis laporan, periode, dan format yang ingin diekspor
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-6">
            {/* Report Type Selection */}
            <div className="space-y-3">
              <Label className="text-base font-medium">Jenis Laporan</Label>
              <div className="grid grid-cols-3 gap-3">
                {[
                  { value: "harian", label: "Laporan Harian", icon: Calendar },
                  {
                    value: "bulanan",
                    label: "Laporan Bulanan",
                    icon: Calendar,
                  },
                  {
                    value: "tahunan",
                    label: "Laporan Tahunan",
                    icon: Calendar,
                  },
                ].map((type) => (
                  <Card
                    key={type.value}
                    className={`cursor-pointer transition-all hover:shadow-md ${
                      exportConfig.reportType === type.value
                        ? "ring-2 ring-blue-500 bg-blue-50 dark:bg-blue-950"
                        : "hover:bg-slate-50 dark:hover:bg-slate-800"
                    }`}
                    onClick={() =>
                      setExportConfig((prev) => ({
                        ...prev,
                        reportType: type.value as any,
                      }))
                    }
                  >
                    <CardContent className="p-4 text-center">
                      <type.icon className="h-8 w-8 mx-auto mb-2 text-blue-500" />
                      <p className="text-sm font-medium">{type.label}</p>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>

            <Separator />

            {/* Sections Selection */}
            <div className="space-y-3">
              <Label className="text-base font-medium">Bagian Laporan</Label>
              <div className="grid grid-cols-3 gap-4">
                {[
                  {
                    key: "penjualan",
                    label: "Data Penjualan",
                    description: "Transaksi penjualan dan pendapatan",
                  },
                  {
                    key: "pembelian",
                    label: "Data Pembelian",
                    description: "Transaksi pembelian dan pengeluaran",
                  },
                  {
                    key: "produk",
                    label: "Data Produk",
                    description: "Stok, penjualan, dan keuntungan produk",
                  },
                ].map((section) => (
                  <div
                    key={section.key}
                    className="flex items-start space-x-3 p-3 border rounded-lg"
                  >
                    <Checkbox
                      id={section.key}
                      checked={
                        exportConfig.sections[
                          section.key as keyof typeof exportConfig.sections
                        ]
                      }
                      onCheckedChange={(checked) =>
                        setExportConfig((prev) => ({
                          ...prev,
                          sections: {
                            ...prev.sections,
                            [section.key]: checked,
                          },
                        }))
                      }
                    />
                    <div className="space-y-1">
                      <Label
                        htmlFor={section.key}
                        className="text-sm font-medium cursor-pointer"
                      >
                        {section.label}
                      </Label>
                      <p className="text-xs text-slate-500">
                        {section.description}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <Separator />

            {/* Format and Options */}
            <div className="grid grid-cols-2 gap-6">
              <div className="space-y-3">
                <Label className="text-base font-medium">Format Export</Label>
                <Select
                  value={exportConfig.format}
                  onValueChange={(value) =>
                    setExportConfig((prev) => ({
                      ...prev,
                      format: value as any,
                    }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="excel">
                      <div className="flex items-center gap-2">
                        <FileSpreadsheet className="h-4 w-4" />
                        Excel (.xlsx)
                      </div>
                    </SelectItem>
                    <SelectItem value="csv">
                      <div className="flex items-center gap-2">
                        <FileText className="h-4 w-4" />
                        CSV (.csv)
                      </div>
                    </SelectItem>
                    <SelectItem value="pdf" disabled>
                      <div className="flex items-center gap-2">
                        <FileImage className="h-4 w-4" />
                        PDF (Segera Hadir)
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-3">
                <Label className="text-base font-medium">Opsi Tambahan</Label>
                <div className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="includeSummary"
                      checked={exportConfig.includeSummary}
                      onCheckedChange={(checked) =>
                        setExportConfig((prev) => ({
                          ...prev,
                          includeSummary: checked as boolean,
                        }))
                      }
                    />
                    <Label
                      htmlFor="includeSummary"
                      className="text-sm cursor-pointer"
                    >
                      Sertakan ringkasan
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="includeCharts"
                      checked={exportConfig.includeCharts}
                      onCheckedChange={(checked) =>
                        setExportConfig((prev) => ({
                          ...prev,
                          includeCharts: checked as boolean,
                        }))
                      }
                      disabled
                    />
                    <Label
                      htmlFor="includeCharts"
                      className="text-sm cursor-pointer text-slate-500"
                    >
                      Sertakan grafik (Segera Hadir)
                    </Label>
                  </div>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex justify-between pt-4">
              <Button
                variant="outline"
                onClick={() => setShowExportDialog(false)}
              >
                Batal
              </Button>
              <Button
                onClick={generateAdvancedExport}
                disabled={!Object.values(exportConfig.sections).some(Boolean)}
                className="flex items-center gap-2"
              >
                <Download className="h-4 w-4" />
                Export Laporan
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Import Button */}
      <Dialog>
        <DialogTrigger asChild>
          <Button variant="outline" className="flex items-center gap-2">
            <Upload className="h-4 w-4" />
            Import
          </Button>
        </DialogTrigger>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Import Data</DialogTitle>
            <DialogDescription>
              Upload file Excel atau CSV untuk mengimpor data ke sistem
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="border-2 border-dashed border-slate-300 rounded-lg p-6 text-center">
              <Upload className="h-12 w-12 mx-auto text-slate-400 mb-4" />
              <p className="text-sm text-slate-600 mb-2">
                Klik untuk memilih file atau drag & drop
              </p>
              <p className="text-xs text-slate-500">
                Mendukung format: .xlsx, .xls, .csv
              </p>
              <input
                ref={fileInputRef}
                type="file"
                accept=".xlsx,.xls,.csv"
                onChange={handleImport}
                className="hidden"
              />
              <Button
                variant="outline"
                onClick={() => fileInputRef.current?.click()}
                className="mt-4"
              >
                Pilih File
              </Button>
            </div>

            {isImporting && (
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span>Mengimpor data...</span>
                  <span>{importProgress}%</span>
                </div>
                <Progress value={importProgress} />
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>

      {/* Additional Actions */}
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" size="icon">
            <Share2 className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem onClick={printReport}>
            <Printer className="mr-2 h-4 w-4" />
            Print Laporan
          </DropdownMenuItem>
          <DropdownMenuItem onClick={shareReport}>
            <Share2 className="mr-2 h-4 w-4" />
            Bagikan Laporan
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Export Progress Dialog */}
      {isExporting && (
        <Dialog open={isExporting}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <Download className="h-5 w-5" />
                Mengekspor Laporan
              </DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div className="flex items-center justify-between text-sm">
                <span>Memproses data...</span>
                <span>{exportProgress}%</span>
              </div>
              <Progress value={exportProgress} />
              <p className="text-sm text-slate-600">
                Mohon tunggu, sedang memproses dan mengunduh file laporan.
              </p>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
};
