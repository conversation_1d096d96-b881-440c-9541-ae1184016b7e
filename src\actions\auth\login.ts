"use server";

import { z } from "zod";
import { LoginSchema } from "@/schemas/zod";
import { signIn } from "@/lib/auth";
import { AuthError } from "next-auth";
import { getUserByEmail } from "@/lib/user";

export const login = async (values: z.infer<typeof LoginSchema>) => {
  // Validasi input menggunakan Zod schema
  const validatedFields = LoginSchema.safeParse(values);
  // Return error jika validasi gagal
  if (!validatedFields.success) {
    return { error: "Input tidak valid!" };
  }

  // Destructure data yang sudah tervalidasi
  const { email, password } = validatedFields.data;
  // Check if user exists and is verified
  const existingUser = await getUserByEmail(email);

  if (!existingUser || !existingUser.emailVerified) {
    return {
      error: "Akun belum terverifikasi!\nSilakan cek email untuk verifikasi.",
    };
  }

  // Jika pengguna tidak ditemukan, lempar error
  if (!existingUser || !existingUser.password) {
    return { error: "User tidak ditemukan atau password salah!" };
  }

  try {
    try {
      // Use signIn with redirect: false to prevent automatic redirects
      const result = await signIn("credentials", {
        redirect: false,
        email,
        password,
      });

      // Return success
      return {
        success: "Login berhasil!",
        redirectTo: "/dashboard",
      };
    } catch (error) {
      if (error instanceof AuthError) {
        switch (error.type) {
          case "CredentialsSignin":
            return { error: "Email atau password salah!" };
          default:
            return { error: "Ada yang salah!" };
        }
      }

      // Check if this is a redirect error (which is actually a success)
      if (error instanceof Error && error.message?.includes("NEXT_REDIRECT")) {
        return {
          success: "Login berhasil!",
          redirectTo: "/dashboard",
        };
      }

      console.error("Error during login:", error);
      return { error: "Terjadi kesalahan saat login!" };
    }
  } catch (error) {
    console.error("Error during login:", error);
    return { error: "Terjadi kesalahan saat login!" };
  }
};
