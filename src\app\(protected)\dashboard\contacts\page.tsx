import React from "react";
import { Metadata } from "next";
import DashboardLayout from "@/components/layout/dashboardlayout";
import ContactsPage from "@/components/pages/dashboard/contacts/contacts";
import { auth } from "@/lib/auth";
import { redirect } from "next/navigation";
import { getCustomers } from "@/actions/entities/customers";
import { getSuppliers } from "@/actions/entities/suppliers";
import { getEmployees } from "@/actions/entities/employee";

export const metadata: Metadata = {
  title: "Kontak | Kasir Online",
  description: "Kelola data pelanggan, supplier, dan karyawan <PERSON>",
};

const Contacts = async () => {
  const session = await auth();
  if (!session?.user?.id) {
    redirect("/login");
  }

  // Fetch all contact data
  const [customersResult, suppliersResult, employeesResult] = await Promise.all([
    getCustomers(),
    getSuppliers(),
    getEmployees(),
  ]);

  if (customersResult.error) {
    console.error("Error fetching customers:", customersResult.error);
  }

  if (suppliersResult.error) {
    console.error("Error fetching suppliers:", suppliersResult.error);
  }

  if (employeesResult.error) {
    console.error("Error fetching employees:", employeesResult.error);
  }

  return (
    <DashboardLayout>
      <ContactsPage
        customers={customersResult.customers || []}
        suppliers={suppliersResult.suppliers || []}
        employees={employeesResult.employees || []}
      />
    </DashboardLayout>
  );
};

export default Contacts;
