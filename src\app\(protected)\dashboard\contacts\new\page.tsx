import React from "react";
import { Metada<PERSON> } from "next";
import DashboardLayout from "@/components/layout/dashboardlayout";
import NewContactPage from "@/components/pages/dashboard/contacts/new/new-contact";
import { auth } from "@/lib/auth";
import { redirect } from "next/navigation";

export const metadata: Metadata = {
  title: "Tambah Kontak | Kasir Online",
  description: "Tambahkan kontak baru (pelanggan, supplier, atau karyawan)",
};

const NewContact = async () => {
  const session = await auth();
  if (!session?.user?.id) {
    redirect("/login");
  }

  return (
    <DashboardLayout>
      <NewContactPage />
    </DashboardLayout>
  );
};

export default NewContact;
